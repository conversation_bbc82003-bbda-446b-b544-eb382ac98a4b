{"pdf_info": [{"para_blocks": [{"bbox": [139, 78, 455, 96], "type": "title", "lines": [{"bbox": [139, 78, 455, 96], "spans": [{"bbox": [139, 78, 455, 96], "type": "text", "content": "基于WebGIS的水利地理信息系统研究与实现"}]}], "index": 0, "level": 1}, {"bbox": [236, 103, 358, 116], "type": "text", "lines": [{"bbox": [236, 103, 358, 116], "spans": [{"bbox": [236, 103, 358, 116], "type": "text", "content": "蔡勇，胡晓荣，谈帅"}]}], "index": 1}, {"bbox": [164, 119, 429, 132], "type": "text", "lines": [{"bbox": [164, 119, 429, 132], "spans": [{"bbox": [164, 119, 429, 132], "type": "text", "content": "(江苏省基础地理信息中心，江苏，南京，210013)"}]}], "index": 2}, {"bbox": [88, 136, 506, 179], "type": "text", "lines": [{"bbox": [88, 136, 506, 179], "spans": [{"bbox": [88, 136, 506, 179], "type": "text", "content": "摘要随着水利事业的迅猛发展，水利设施建设、防汛抗旱指挥和水资源管理等工作的开展需要管理和发布大量数据，要求功能强大的水利地理信息系统。文章在提出了基于J2EE架构和Flex客户端开发技术开发水利地理系统的思路，实现了水利地理信息的共享和各种查询、分析功能。"}]}], "index": 3}, {"bbox": [89, 182, 233, 194], "type": "text", "lines": [{"bbox": [89, 182, 233, 194], "spans": [{"bbox": [89, 182, 233, 194], "type": "text", "content": "关键词WebGIS；服务共享；分布式"}]}], "index": 4}, {"bbox": [88, 206, 139, 222], "type": "title", "lines": [{"bbox": [88, 206, 139, 222], "spans": [{"bbox": [88, 206, 139, 222], "type": "text", "content": "1 引言"}]}], "index": 5, "level": 1}, {"bbox": [88, 234, 506, 326], "type": "text", "lines": [{"bbox": [88, 234, 506, 326], "spans": [{"bbox": [88, 234, 506, 326], "type": "text", "content": "近年来水利事业的发展迅速，水利工程建设、水资源管理和防汛抗旱等方面的数据量不断增大，各个水利部门及主管单位之间信息不能共享，存在着数据建设重复投资、盲目建设的现象，数据获取难、更新难，急需通过水利地理信息系统实现信息服务的共享，将分散的地理信息数据资源整合为逻辑上集中、物理上分布的统一地理信息资源，有力促进本地区地理信息资源共享与应用，有效避免“信息孤岛”现象，充分发挥水利信息在工农业生产、政府宏观决策、应急管理、社会公益服务、产业升级拓展、人民生活改善等方面的保障服务作用。"}]}], "index": 6}, {"bbox": [88, 333, 506, 410], "type": "text", "lines": [{"bbox": [88, 333, 506, 410], "spans": [{"bbox": [88, 333, 506, 410], "type": "text", "content": "基于以上背景，本文以《江苏省水利地理信息系统一期工程》为基础，提出了基于WebGIS的省级水利地理信息发布系统的思想。系统基于J2EE架构实现了功能强大的服务端功能，包括数据库的读写、各种格式文件的处理，基于Flex富客户端开发技术实现了界面友好、操作简便的客户端功能。基于ArcGIS Server实现了地图服务和功能服务的共享，解决了地图和功能服务的分布式访问问题。"}]}], "index": 7}, {"bbox": [88, 423, 168, 440], "type": "title", "lines": [{"bbox": [88, 423, 168, 440], "spans": [{"bbox": [88, 423, 168, 440], "type": "text", "content": "2 系统框架"}]}], "index": 8, "level": 1}, {"type": "image", "bbox": [161, 450, 432, 634], "blocks": [{"bbox": [161, 450, 432, 634], "lines": [{"bbox": [161, 450, 432, 634], "spans": [{"bbox": [161, 450, 432, 634], "type": "image", "image_path": "890dd2fa4a59a0897bd6e6f90ab35d49bbf938be64f6f2c6435dd091779f440f.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}, {"bbox": [109, 639, 409, 652], "type": "text", "lines": [{"bbox": [109, 639, 409, 652], "spans": [{"bbox": [109, 639, 409, 652], "type": "text", "content": "系统采用WebGIS中典型的三层架构：数据层、服务层、表现层。"}]}], "index": 10}, {"bbox": [88, 660, 506, 767], "type": "text", "lines": [{"bbox": [88, 660, 506, 767], "spans": [{"bbox": [88, 660, 506, 767], "type": "text", "content": "1. 数据层主要指水利地理数据库。主要包括：与水利相关的基础地理信息数据库（DLG、DOM、DEM）、水利公共类数据库、水利专业类数据库等，是系统的底层基础——数据库服务器端。2. 服务层主要指各类应用服务与数据层之间的连接接口。主要包括各类专业应用组件、服务接口、数据交换接口等，是系统中间层——GIS服务器端。3. 表现层主要指各类专业应用结果的展示界面。包括查询结果、专题图生成结果、分析结果、图形输出结果等的表现界面，是系统最上层——客户端/浏览器端。"}]}], "index": 11}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 0}, {"para_blocks": [{"bbox": [88, 78, 168, 95], "type": "title", "lines": [{"bbox": [88, 78, 168, 95], "spans": [{"bbox": [88, 78, 168, 95], "type": "text", "content": "3 数据情况"}]}], "index": 0, "level": 1}, {"bbox": [88, 105, 506, 167], "type": "text", "lines": [{"bbox": [88, 105, 506, 167], "spans": [{"bbox": [88, 105, 506, 167], "type": "text", "content": "系统所涉及的数据总体上包括空间数据和属性数据两类。其中，空间数据又分为基础地理数据和水利地理数据。由于水利要素的属性数据可以依据要素编码与空间对象相关联，因此无需考虑水利要素的属性数据分类情况。数据库采用分布式访问技术，各个部门的专业数据存贮在各自主管的服务器中，通过系统进行统一集成展示。"}]}], "index": 1}, {"bbox": [88, 174, 506, 219], "type": "text", "lines": [{"bbox": [88, 174, 506, 219], "spans": [{"bbox": [88, 174, 506, 219], "type": "text", "content": "项目涉及的基础地理信息主要包括DLG、DEM、DOM三类，水利地理信息主要指矢量图，包含了水利行业7大类基本数据，即水文、水利工程、水利公共类、防汛抗旱、水资源、农村水利、水利规划。"}]}], "index": 2}, {"bbox": [108, 227, 325, 241], "type": "text", "lines": [{"bbox": [108, 227, 325, 241], "spans": [{"bbox": [108, 227, 325, 241], "type": "text", "content": "因此，江苏省水利地理信息系统数据主要包括："}]}], "index": 3}, {"type": "table", "bbox": [135, 248, 458, 387], "blocks": [{"bbox": [135, 248, 458, 387], "lines": [{"bbox": [135, 248, 458, 387], "spans": [{"bbox": [135, 248, 458, 387], "type": "table", "html": "<table><tr><td>序号</td><td>数据名称</td><td>存储方式</td><td>数据形式</td></tr><tr><td>1</td><td>DLG 数据</td><td>数据库</td><td>瓦片地图、矢量</td></tr><tr><td>2</td><td>DEM 数据</td><td>数据库</td><td>栅格</td></tr><tr><td>3</td><td>DOM 数据</td><td>数据库</td><td>瓦片地图</td></tr><tr><td>4</td><td>水利专题数据</td><td>数据库</td><td>矢量</td></tr><tr><td>5</td><td>三维场景数据</td><td>文件</td><td>栅格</td></tr><tr><td>6</td><td>三维模型数据</td><td>文件</td><td>矢量</td></tr></table>", "image_path": "81b0ce2ed25edb481fa6e5423aa7f15e25b86164919c4584184588db46a60717.jpg"}]}], "index": 4, "type": "table_body"}], "index": 4}, {"bbox": [108, 389, 505, 403], "type": "text", "lines": [{"bbox": [108, 389, 505, 403], "spans": [{"bbox": [108, 389, 505, 403], "type": "text", "content": "空间数据中只保留编码、名称字段，属性数据表中需包含编码字段，二者通过编码关联。"}]}], "index": 5}, {"type": "table", "bbox": [148, 413, 458, 532], "blocks": [{"bbox": [148, 413, 458, 532], "lines": [{"bbox": [148, 413, 458, 532], "spans": [{"bbox": [148, 413, 458, 532], "type": "table", "html": "<table><tr><td colspan=\"5\">主表</td></tr><tr><td>OID</td><td>编码</td><td>名称</td><td>Shape</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td colspan=\"5\">子表</td></tr><tr><td>编码</td><td>属性 1</td><td>属性 2</td><td>属性 3</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table>", "image_path": "446f99df845f0cf0a805d4130171d6e0d5735b49a10213fbf7f726813d51560e.jpg"}]}], "index": 6, "type": "table_body"}], "index": 6}, {"bbox": [88, 535, 506, 580], "type": "text", "lines": [{"bbox": [88, 535, 506, 580], "spans": [{"bbox": [88, 535, 506, 580], "type": "text", "content": "分布式数据库包含了实时水雨情、历史水文、遥测水雨情、水利工程、水利公共类、水资源、系统数据库这7个数据库，包含了ORALCE、SYBASE、SQLServer这三种类型的数据库。"}]}], "index": 7}, {"type": "table", "bbox": [101, 587, 493, 763], "blocks": [{"bbox": [101, 587, 493, 763], "lines": [{"bbox": [101, 587, 493, 763], "spans": [{"bbox": [101, 587, 493, 763], "type": "table", "html": "<table><tr><td>编 码</td><td>数据库 类型</td><td>主机</td><td>数据库实例名</td><td>账号</td><td>密码</td><td>数据库名称</td></tr><tr><td>1</td><td>oracle</td><td>10.32.**.***</td><td>kfbserver1db</td><td>***</td><td>***</td><td>实时水雨情</td></tr><tr><td>2</td><td>oracle</td><td>10.32.***</td><td>kfbserver1db</td><td>***</td><td>***</td><td>历史水文数据</td></tr><tr><td>3</td><td>sybase</td><td>10.32.***</td><td>jstmdb</td><td>***</td><td>***</td><td>遥测水雨情</td></tr><tr><td>4</td><td>oracle</td><td>10.32.***</td><td>kfbserver1db</td><td>***</td><td>***</td><td>水利工程</td></tr><tr><td>6</td><td>oracle</td><td>10.32.***</td><td>kfbserver1db</td><td>***</td><td>***</td><td>水利公共类</td></tr></table>", "image_path": "8af6b8499f6021370d5e05e18023e53cb2311de8b36a0d19cdc2125dc8459e42.jpg"}]}], "index": 8, "type": "table_body"}], "index": 8}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 1}, {"para_blocks": [{"type": "table", "bbox": [101, 70, 492, 121], "blocks": [{"bbox": [101, 70, 492, 121], "lines": [{"bbox": [101, 70, 492, 121], "spans": [{"bbox": [101, 70, 492, 121], "type": "table", "html": "<table><tr><td>5</td><td>sqlserver</td><td>10.32.**.***</td><td>jwpec</td><td>***</td><td>***</td><td rowspan=\"2\">水资源系统数据库</td></tr><tr><td>0</td><td>oracle</td><td>10.32.**.***</td><td>kfserver1db</td><td>***</td><td>***</td></tr></table>", "image_path": "e4016629aae76ede5c2a24a82e79c82bb1bb2c0c8df4e1bd2d4909ac60b4f308.jpg"}]}], "index": 0, "type": "table_body"}], "index": 0}, {"bbox": [88, 131, 168, 148], "type": "title", "lines": [{"bbox": [88, 131, 168, 148], "spans": [{"bbox": [88, 131, 168, 148], "type": "text", "content": "4 关键技术"}]}], "index": 1, "level": 1}, {"bbox": [89, 162, 267, 177], "type": "title", "lines": [{"bbox": [89, 162, 267, 177], "spans": [{"bbox": [89, 162, 267, 177], "type": "text", "content": "4.1 面向服务的分布式数据访问技术"}]}], "index": 2, "level": 1}, {"bbox": [89, 185, 506, 230], "type": "text", "lines": [{"bbox": [89, 185, 506, 230], "spans": [{"bbox": [89, 185, 506, 230], "type": "text", "content": "系统的数据都彼此孤立，基于数据模型及存储方式的差异，具有异构性、分布性和自治性，难以实现资源的共享。因此，将这些数据进行有效的集成，使之作为一个统一的数据平台为系统的应用提供数据支撑服务是目前综合型信息系统研究的焦点之一。"}]}], "index": 3}, {"bbox": [88, 238, 506, 299], "type": "text", "lines": [{"bbox": [88, 238, 506, 299], "spans": [{"bbox": [88, 238, 506, 299], "type": "text", "content": "项目采用基于面向服务架构(SOA)的中间层模式实现水利业务数据的集成。基于SOA的数据集成支持对异构数据类型的访问，通过利用标准化接口，提供一个高度灵活的抽象层，把数据逻辑与业务逻辑分离，把数据访问和处理以服务的形式提供给外部使用者，从而增加了系统的灵活性和重用性，实现了应用和服务的松散耦合。"}]}], "index": 4}, {"bbox": [88, 310, 312, 324], "type": "title", "lines": [{"bbox": [88, 310, 312, 324], "spans": [{"bbox": [88, 310, 312, 324], "type": "text", "content": "4.2 基于ArcGIS Server的空间信息分析技术"}]}], "index": 5, "level": 1}, {"bbox": [88, 332, 506, 409], "type": "text", "lines": [{"bbox": [88, 332, 506, 409], "spans": [{"bbox": [88, 332, 506, 409], "type": "text", "content": "项目空间信息分析功能的开发是基于ESRI的ArcServer平台和Flex开发环境进行的。Flex支持ArcServer中REST的服务，由此ESRI公司推出了ArcServer Flex API，它支持的Map服务可以是动态或瓦片的，地图可以是任意坐标系统。由于ArcServer Flex API目前版本基本不具备可扩展性，要想实现客户端/浏览器(即B/S)模式的空间信息分析服务只能依靠模块处理（Geoprocessing Task）功能。"}]}], "index": 6}, {"bbox": [88, 417, 506, 508], "type": "text", "lines": [{"bbox": [88, 417, 506, 508], "spans": [{"bbox": [88, 417, 506, 508], "type": "text", "content": "Geoprocessing是一套对已有数据进行分析后获取其他信息的转换工具，它能够通过分析处理已经存在的数据，在新的数据集中产生分析结果。它通过脚本Python或者ModelBuilder来设计，在ArcToolbox中进行管理，能够处理ArcGIS支持的所有数据类型，通过Geoprocessing可以实现大部分ArcGIS的功能。通过ArcGIS Server将设计好Geoprocessing进行对外发布后，系统就可以调用该服务功能实现各种空间分析及数据处理的功能。"}]}], "index": 7}, {"bbox": [89, 519, 209, 534], "type": "title", "lines": [{"bbox": [89, 519, 209, 534], "spans": [{"bbox": [89, 519, 209, 534], "type": "text", "content": "4.3 基于Flex RIA技术"}]}], "index": 8, "level": 1}, {"bbox": [88, 542, 506, 603], "type": "text", "lines": [{"bbox": [88, 542, 506, 603], "spans": [{"bbox": [88, 542, 506, 603], "type": "text", "content": "Flex是由Adobe公司提出的富互联网应用程序（Rich Internet Application）平台，所谓RIA就是应用程序结合了桌面应用程序的反应快、交互性强的优点与Web应用程序的传播范围广及容易传播的特性，简化并改进了Web应用程序的用户交互，提供更丰富、更具有交互性和响应性的用户体验。"}]}], "index": 9}, {"bbox": [88, 610, 506, 672], "type": "text", "lines": [{"bbox": [88, 610, 506, 672], "spans": [{"bbox": [88, 610, 506, 672], "type": "text", "content": "Flex采用GUI界面开发，使用基于XML的MXML语言。Flex具有多种组件，可实现Web Services，远程对象，拖放，列排序，图表等功能。开发人员使用MXML以及GUI设计器像设计Windows窗体一样设计应用程序界面，通过ActionScript实现客户端的应用逻辑，Flex编译器将MXML以及ActionScript代码编译成Flash文件在IE等浏览器中运行。"}]}], "index": 10}, {"bbox": [89, 679, 506, 724], "type": "text", "lines": [{"bbox": [89, 679, 506, 724], "spans": [{"bbox": [89, 679, 506, 724], "type": "text", "content": "Flex作为主流的RIA平台主要有如下特点：部署方便、类似桌面应用的用户体验、完全面向对象的开发方式，支持接口，继承，重载等现在面向对象高级语言所有的特性，加上内置丰富的类库使得开发非常方便。"}]}], "index": 11}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 2}, {"para_blocks": [{"bbox": [89, 75, 256, 90], "type": "title", "lines": [{"bbox": [89, 75, 256, 90], "spans": [{"bbox": [89, 75, 256, 90], "type": "text", "content": "4.4 基于Skyline的三维场景渲染"}]}], "index": 0, "level": 1}, {"bbox": [88, 98, 506, 159], "type": "text", "lines": [{"bbox": [88, 98, 506, 159], "spans": [{"bbox": [88, 98, 506, 159], "type": "text", "content": "基于Skyline建立的三维场景的主要优点是不同比例尺数据与多级分辨率影像无缝分级显示，通过网络进行3D数据的传输，对低带宽的情况能够进行最优化。系统开发出的三维场景渲染模块在Skyline Developer提供的开发接口基础上，读取、解析系统二维模块生成并存储在服务器端的场景信息文件，实现了Web页面的三维场景渲染。"}]}], "index": 1}, {"bbox": [88, 166, 506, 211], "type": "text", "lines": [{"bbox": [88, 166, 506, 211], "spans": [{"bbox": [88, 166, 506, 211], "type": "text", "content": "场景的三维展现、应急救援路线、疏散路线等的动态三维渲染，增强了应急指挥人员对应急事件周边环境的浸入感和体验感，可以帮助应急指挥人员更加直观的分析事件周边情况，从而快速、准确的做出反应和决策。"}]}], "index": 2}, {"bbox": [88, 226, 168, 243], "type": "title", "lines": [{"bbox": [88, 226, 168, 243], "spans": [{"bbox": [88, 226, 168, 243], "type": "text", "content": "5 系统功能"}]}], "index": 3, "level": 1}, {"bbox": [88, 257, 324, 271], "type": "title", "lines": [{"bbox": [88, 257, 324, 271], "spans": [{"bbox": [88, 257, 324, 271], "type": "text", "content": "5.1 水、雨、工、灾情的信息管理、查询和分析。"}]}], "index": 4, "level": 1}, {"bbox": [88, 280, 506, 340], "type": "text", "lines": [{"bbox": [88, 280, 506, 340], "spans": [{"bbox": [88, 280, 506, 340], "type": "text", "content": "5.1 水、雨、工、灾情的信息管理、查询和分析。提供可视化的图形查询界面，丰富的信息表述方式。查询结果可以链接相关的数据库，对信息进行拓展，数据库查询结果可以进行统计，并以饼图、柱状图等形式展示。提供多种分析工具如缓冲分析、路径分析、叠加分析、降雨量等值线、泰森多边形计算分析等功能，有效的辅助了水利部门平时管理、战时调度指挥和决策。"}]}], "index": 5}, {"bbox": [88, 352, 196, 366], "type": "title", "lines": [{"bbox": [88, 352, 196, 366], "spans": [{"bbox": [88, 352, 196, 366], "type": "text", "content": "5.2 洪水的预测预报。"}]}], "index": 6, "level": 1}, {"bbox": [88, 375, 506, 420], "type": "text", "lines": [{"bbox": [88, 375, 506, 420], "spans": [{"bbox": [88, 375, 506, 420], "type": "text", "content": "根据实时水雨情和遥测水雨情数据，提供洪水预警分析功能。当某个区域的降雨超过一定的限度，水文站点、报讯站点的实测数据会自动、实时的与预警极值进行对比，超过警戒限时，该站点在地图上会以红色闪烁的方式表示。"}]}], "index": 7}, {"bbox": [88, 427, 505, 456], "type": "text", "lines": [{"bbox": [88, 427, 505, 456], "spans": [{"bbox": [88, 427, 505, 456], "type": "text", "content": "系统同时提供给定降水区域和降水量所产生的径流和洪水的时空分布模拟供，模拟洪水的淹没范围。"}]}], "index": 8}, {"bbox": [88, 468, 208, 482], "type": "title", "lines": [{"bbox": [88, 468, 208, 482], "spans": [{"bbox": [88, 468, 208, 482], "type": "text", "content": "5.3 防汛抢险救灾指挥。"}]}], "index": 9, "level": 1}, {"bbox": [88, 491, 506, 520], "type": "text", "lines": [{"bbox": [88, 491, 506, 520], "spans": [{"bbox": [88, 491, 506, 520], "type": "text", "content": "5.3 防汛抢险救灾指挥。可以利用灾害分析模型结合GIS功能进行灾前分析，也可以利用GIS的网络分析功能确定救灾物资调配的最佳路径，还可以为受灾人员、财产的安全有效转移提供决策依据。"}]}], "index": 10}, {"bbox": [88, 532, 218, 546], "type": "title", "lines": [{"bbox": [88, 532, 218, 546], "spans": [{"bbox": [88, 532, 218, 546], "type": "text", "content": "5.4 为防洪规划提供依据。"}]}], "index": 11, "level": 1}, {"bbox": [88, 555, 505, 583], "type": "text", "lines": [{"bbox": [88, 555, 505, 583], "spans": [{"bbox": [88, 555, 505, 583], "type": "text", "content": "5.4 为防洪规划提供依据。可以根据预告设定的前提条件，进行某一区域范围内的静态或者动态的模拟，为有效地设置拦洪设施的点位和选择分洪、泄洪措施提供辅助决策支持。"}]}], "index": 12}, {"bbox": [88, 596, 161, 608], "type": "title", "lines": [{"bbox": [88, 596, 161, 608], "spans": [{"bbox": [88, 596, 161, 608], "type": "text", "content": "5.5 三维展示"}]}], "index": 13, "level": 1}, {"bbox": [88, 618, 505, 647], "type": "text", "lines": [{"bbox": [88, 618, 505, 647], "spans": [{"bbox": [88, 618, 505, 647], "type": "text", "content": "5.5 三维展示三维展示系统主要是展示重点水利工程的三维场景。同时可进行相关查询，查询结果在三维场景中展示，另外可进行坡度计算、通视分析和土石方计算等。"}]}], "index": 14}, {"type": "image", "bbox": [133, 657, 460, 759], "blocks": [{"bbox": [133, 657, 460, 759], "lines": [{"bbox": [133, 657, 460, 759], "spans": [{"bbox": [133, 657, 460, 759], "type": "image", "image_path": "cf31932fdf1b91e3c7a69324df5ef8a23b4994f6a10e635e6d9a190be36865fe.jpg"}]}], "index": 15, "type": "image_body"}], "index": 15}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 3}, {"para_blocks": [{"bbox": [88, 78, 139, 95], "type": "title", "lines": [{"bbox": [88, 78, 139, 95], "spans": [{"bbox": [88, 78, 139, 95], "type": "text", "content": "6 结论"}]}], "index": 0, "level": 1}, {"bbox": [88, 105, 506, 198], "type": "text", "lines": [{"bbox": [88, 105, 506, 198], "spans": [{"bbox": [88, 105, 506, 198], "type": "text", "content": "本文以江苏省水利行业的相关数据为研究对象，通过使用Flex开发技术，结合J2EE架构及其他相关技术等，实现了基于B/S的水利地理信息系统的开发，为水利系统的工作人员提供各种水利信息的浏览查询和分析，系统改变了现有的部分工作方式，完成了地图常用功能的设计，包括地图放大、缩小、鹰眼等；并按照用户的需要，扩展了信息查询、空间分析、资源共享、三维展示等功能，满足了实际应用的需要；按照水利行业标准，建立了规范的专题应用数据库，实现了图形与属性数据的关联。"}]}], "index": 1}, {"bbox": [88, 206, 506, 250], "type": "text", "lines": [{"bbox": [88, 206, 506, 250], "spans": [{"bbox": [88, 206, 506, 250], "type": "text", "content": "21世纪是信息时代，地理信息系统技术在水利方面的应用将越来越广泛，尤其在水利政务、防汛抗旱减灾、水资源监控调度等方面是传统技术所不能比拟的，基于WebGIS的水利地理信息系统拥有广阔的发展空间。"}]}], "index": 2}, {"bbox": [271, 259, 321, 272], "type": "title", "lines": [{"bbox": [271, 259, 321, 272], "spans": [{"bbox": [271, 259, 321, 272], "type": "text", "content": "参考文献"}]}], "index": 3, "level": 1}, {"bbox": [96, 275, 506, 318], "type": "text", "lines": [{"bbox": [96, 275, 506, 318], "spans": [{"bbox": [96, 275, 506, 318], "type": "text", "content": "[1] 黄杏元.地理信息系统概论[M].北京：北京高等教育出版社，2001  [2] 汤国安.地理信息系统空间分析实验教程[M].北京：科学出版社，2001  [3] 黄奕华.基于WEBGIS的广东省水利综合数据库系统的设计与实现[J].计算机应用，2007（8）：65- 68"}]}], "index": 4}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 4}], "_backend": "vlm", "_version_name": "2.0.6"}